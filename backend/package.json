{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "dotenv": "^17.2.3", "jsonwebtoken": "^9.0.2", "mongodb": "^6.20.0", "mongoose": "^8.18.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "bcrypt": "^6.0.0", "express": "^5.1.0", "nodemailer": "^7.0.6"}, "devDependencies": {"nodemon": "^3.1.10"}}